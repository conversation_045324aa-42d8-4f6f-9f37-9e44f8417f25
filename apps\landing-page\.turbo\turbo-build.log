
> @freela/landing-page@1.0.0 build
> next build

   Linting and checking validity of types ...

./src/utils/errorFilter.ts
7:30  Warning: Unexpected console statement.  no-console
8:29  Warning: Unexpected console statement.  no-console
37:5  Warning: Unexpected console statement.  no-console
37:31  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
44:5  Warning: Unexpected console statement.  no-console
44:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
73:3  Warning: Unexpected console statement.  no-console
74:3  Warning: Unexpected console statement.  no-console

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
   ▲ Next.js 14.0.3

   Creating an optimized production build ...
