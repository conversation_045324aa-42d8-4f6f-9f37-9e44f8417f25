
> @freela/expert-dashboard@1.0.0 build
> next build

   ▲ Next.js 14.0.3

   Creating an optimized production build ...
 ⚠ Compiled with warnings

   Linting and checking validity of types ...
./src/app/dashboard/analytics/page.tsx
Attempted import error: 'TrendingUpIcon' is not exported from '__barrel_optimize__?names=CalendarIcon,ClockIcon,CurrencyDollarIcon,EyeIcon,StarIcon,TrendingUpIcon!=!@heroicons/react/24/outline' (imported as 'TrendingUpIcon').

Import trace for requested module:
./src/app/dashboard/analytics/page.tsx

./src/app/dashboard/analytics/page.tsx
Attempted import error: 'TrendingUpIcon' is not exported from '__barrel_optimize__?names=CalendarIcon,ClockIcon,CurrencyDollarIcon,EyeIcon,StarIcon,TrendingUpIcon!=!@heroicons/react/24/outline' (imported as 'TrendingUpIcon').

Import trace for requested module:
./src/app/dashboard/analytics/page.tsx


 ⚠ The Next.js plugin was not detected in your ESLint configuration. See https://nextjs.org/docs/basic-features/eslint#migrating-existing-config

Failed to compile.

./src/app/dashboard/analytics/page.tsx
5:3  Error: 'ChartBarIcon' is defined but never used.  @typescript-eslint/no-unused-vars
7:3  Error: 'UserGroupIcon' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/dashboard/bookings/page.tsx
111:7  Warning: Unexpected console statement.  no-console
126:9  Warning: Unexpected console statement.  no-console
141:7  Warning: Unexpected console statement.  no-console

./src/app/dashboard/earnings/page.tsx
12:10  Error: 'LoadingState' is defined but never used.  @typescript-eslint/no-unused-vars
140:7  Warning: Unexpected console statement.  no-console

./src/app/dashboard/messages/page.tsx
8:3  Error: 'UserIcon' is defined but never used.  @typescript-eslint/no-unused-vars
9:3  Error: 'ClockIcon' is defined but never used.  @typescript-eslint/no-unused-vars
11:10  Error: 'LoadingState' is defined but never used.  @typescript-eslint/no-unused-vars
185:7  Warning: Unexpected console statement.  no-console

./src/app/error.tsx
13:5  Warning: Unexpected console statement.  no-console

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\Freela\apps\expert-dashboard
npm error workspace @freela/expert-dashboard@1.0.0
npm error location C:\Users\<USER>\Documents\Freela\apps\expert-dashboard
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
