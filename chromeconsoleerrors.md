serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
background.js:2 Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
Unchecked runtime.lastError: No tab with id: 789786023.
Unchecked runtime.lastError: No tab with id: 789786023.
Unchecked runtime.lastError: No tab with id: 789786023.
contentscript.bundle.js:1 Uncaught (in promise) Object
client.js:2 Warning: Prop `d` did not match. Server: "M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" Client: "M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
    at path
    at svg
    at SunIcon (webpack-internal:///../../node_modules/@heroicons/react/24/outline/esm/SunIcon.js:5:3)
    at button
    at div
    at div
    at nav
    at header
    at Header (webpack-internal:///./src/components/Layout/Header.tsx:28:88)
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at App (webpack-internal:///./src/pages/_app.tsx:31:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11) 

See more info here: https://nextjs.org/docs/messages/react-hydration-error
console.error @ client.js:2
:3000/favicon-32x32.png:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)
:3000/favicon-16x16.png:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)
:3000/android-chrome-192x192.png:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)
localhost/:1 Error while trying to use the following icon from the Manifest: http://localhost:3000/android-chrome-192x192.png (Download error or resource isn't a valid image)
client.js:2 [ChromeTransport] connectChrome error: Error: MetaMask extension not found
    at inpage.js:1:16512
console.error @ client.js:2
